import { useState, useCallback, useEffect, useRef } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { useTranslation } from "@1f/react-sdk";
import { IList } from "../../../interfaces/general.interfaces";
import { IDeadlinesQuery } from "../interfaces/interfaces";
import { getDeadlinesGrid } from "../utils/grids";
import { isEqual } from "lodash";

const useDeadlinesList = (query: IDeadlinesQuery) => {
    const { t } = useTranslation();
    const listDeadlinesRequest = useGetCustom("deadlines/list?noTemplateVars=true");

    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });
    const [error, setError] = useState<string | null>(null);
    const [columnsLoaded, setColumnsLoaded] = useState(false);

    // Ref to track the previous query for comparison
    const previousQueryRef = useRef<IDeadlinesQuery | null>(null);

    const cleanQuery = (query: IDeadlinesQuery) => {
        const entries = Object.entries(query).map(([key, value]) => {
            if (typeof value === 'boolean' && (key === 'poliswebFilter' || key === 'importantOnly')) {
                return value ? [key, "on"] : null;
            }
            // Skip practiceSearch and titleObjectSearch as we'll combine them into object
            if (key === 'practiceSearch' || key === 'titleObjectSearch') {
                return null;
            }
            return [key, value];
        }).filter(entry => {
            if (!entry) return false; // Remove null entries (unchecked checkboxes)
            const [key, value] = entry;
            if (typeof value === 'boolean') {
                return value !== false;
            }
            if (typeof value === 'string') {
                // Always include search fields, even if empty
                if (key === 'object') {
                    return true;
                }
                return value !== "";
            }
            return value !== null && value !== undefined;
        });

        const cleanedQuery = Object.fromEntries(entries as [string, any][]);

        // Handle search parameters - backend only supports 'object' parameter
        const practiceSearch = query.practiceSearch?.trim() || '';
        const titleObjectSearch = query.titleObjectSearch?.trim() || '';

        // Priority: titleObjectSearch works properly (searches in impegno title/text)
        // practiceSearch is a fallback but limited since backend doesn't search practice fields
        let searchValue = '';
        if (titleObjectSearch) {
            searchValue = titleObjectSearch;
        } else if (practiceSearch) {
            // Note: This will only search in impegno text, not in practice-specific fields
            searchValue = practiceSearch;
        }

        cleanedQuery.object = searchValue;

        return cleanedQuery;
    };

    const fetchData = useCallback(async (queryToUse: IDeadlinesQuery) => {
        let cleanedQuery: any;
        try {
            setError(null); // Clear previous errors
            cleanedQuery = cleanQuery(queryToUse);

            console.log('[useDeadlinesList] Sending query to backend:', {
                originalQuery: queryToUse,
                cleanedQuery,
                timestamp: new Date().toISOString()
            });

            const response: any = await listDeadlinesRequest.doFetch(true, cleanedQuery);
            const { currentPage, totalRows } = response.data;



            setList(prev => ({
                ...prev, // Keep existing columns
                rows: currentPage || [],
                totalRows: parseInt(totalRows) || 0,
                page: queryToUse?.page || 0,
                pageIndex: queryToUse?.page || 0,
                pageSize: queryToUse?.pageSize || 10,
            }));
        } catch (err) {
            console.error("[useDeadlinesList] Error fetching deadlines list:", err);
            const errorMessage = err instanceof Error ? err.message : "Errore durante il caricamento dei dati";
            setError(errorMessage);
            setList(prev => ({
                ...prev,
                rows: [],
                totalRows: 0,
            }));
        }
    }, [listDeadlinesRequest]);

    // Load columns only once on mount
    useEffect(() => {
        const loadColumns = async () => {
            try {
                const columns = await getDeadlinesGrid(t);
                setList(prev => ({
                    ...prev,
                    columns: columns
                }));
                setColumnsLoaded(true);
            } catch (error) {

                setColumnsLoaded(true); // Still mark as loaded to prevent infinite loading
            }
        };

        if (!columnsLoaded) {
            loadColumns();
        }
    }, [t, columnsLoaded]);

    // Effect to handle query changes with conditional debouncing
    useEffect(() => {


        // Don't fetch data until columns are loaded
        if (!columnsLoaded) {

            return;
        }

        const prevQuery = previousQueryRef.current;

        // Skip if query hasn't actually changed
        if (prevQuery && isEqual(query, prevQuery)) {

            return;
        }



        // Fetch immediately - debouncing is handled in the component
        fetchData(query);

        previousQueryRef.current = query;

        // No cleanup needed since we removed debouncing
    }, [query, columnsLoaded, fetchData]);

    return {
        list,
        loading: listDeadlinesRequest.loading || !columnsLoaded,
        error,
        fetchData: () => fetchData(query),
    };
};

export default useDeadlinesList;
