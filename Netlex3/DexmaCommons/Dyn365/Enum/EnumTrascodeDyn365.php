<?php

namespace DexmaCommons\Dyn365\Enum;

class EnumTrascodeDyn365
{

    const TRANSCODE = [
        'subject' => [
            'external_sw_id' => 'codiceCliente',
            'partitaiva' => 'partitaIva',
            'codicefiscale' => 'codiceFiscale',
            'codiceb2b' => 'codiceIPASDI',
            'email_pec' => 'emailPEC', // 7
            'type' => 'tipoSoggetto',
            'company_name' => 'ragioneSociale',
            'nome' => 'nome',
            'cognome' => 'cognome',
            'address' => 'indirizzo', // 9
            'cap' => 'cap', // 9
            'city' => 'city', // 9
            'location' => 'localita', // 9
//            'region' => 'regione', // 9
            'province' => 'provincia', // 9
            'nation' => 'nazione', // 9
            'email' => 'email', // 6
            'company_name_extension' => 'estensioneRagioneSociale',
            'location_extension' => 'estensioneLocalita'
        ],
        'invoice' => [
            'type' => 'tipoOrdine',
            'payment_method' => 'metodoPagamento',
            'paid' => 'terminePagamento',
            'sezionale_iva' => 'sezionaleIva',
            'data' => 'dataDocumento',
            'total_' => 'importoTotaleDocumento',
            'totale_iva' => 'importoTotaleIva',
            'is_vat_calculated' => 'ivaCalcolata',
            'archive_code' => 'riferimentoOrdineCliente',
            'project' => 'progetto',
            'id' => 'riferimentoDocumentoOrigine',
            'activity' => 'attivita',
            'logged_user' => 'operatore',
            'causal_sale' => 'causaleVendita',
            'responsibility_area' => 'areaResponsabilita',
            'responsibility_centre' => 'centroResponsabilita',
            'cost_center' => 'centroDiCosto',
            'accounting_detail' => 'dettaglioContabile',

        ],
        'movement' => [
            'numberRow' => 'idRiga',
            'external_code' => 'codiceArticolo',
            'unita_misura' => 'unitaMisura',
            'unita_prezzo' => 'unitaPrezzo',
            'quantita' => 'qta',
            'importo' => 'prezzoUnitario',
            'total' => 'valore',
            'descrizione' => 'descrizione',
            'vat_code' => 'codiceIva',
            'total_iva' => 'importoIvaCalcolata',
            'iva_scorporato' => 'ivaScorporata',
            'iva' => 'fasciaIva',
            'parcella_id' => 'riferimentoDocumentoOrigine',
            'archive_code' => 'progetto',

            'responsibility_area' => 'areaResponsabilita',
            'responsibility_centre' => 'centroResponsabilita',
            'cost_center' => 'centroDiCosto',
            'activity' => 'attivita',
        ]
    ];
}