<?php

namespace DexmaCommons\Dyn365\Handlers;

use Exception;
use GuzzleHttp\Client;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;
use Netlex3\Software\patterns\Services\Settings\SettingsService;
use Zend_Registry;

class Dyn365Handler
{
//    const BASE_URL = 'https://esbtexposed.infocamere.it/fpasWeb/std/service/call';
    const BASE_URL = 'https://esbtexposed.cl.infocamere.it/fpasWeb/std/service/call';
    const MAX_ATTEMPT = 2;
    const AUTH_ERROR = 'Errore nella chiamata di autenticazione SESSION ID nullo';
    const ATTEMPT_TIMEOUT = 200; //milliseconds

    private $handlerName = 'CameraArbitraleDynamic';
    private $handlerVersion = '1.2';
    private $infrastructure;
    private $settingsService;
    private $params;
    private $applogger;

    public function __construct(InfrastructureInterface $infrastructure)
    {
        $this->infrastructure = $infrastructure;
        $this->settingsService = new SettingsService($this->infrastructure);
        $settings = $this->settingsService->getAllSettings();
        $this->params = json_decode($settings['contabilita_esterna_values']);
        $this->applogger = Zend_Registry::get('applogger');
    }

    public function sendInvoice($invoiceId, $data)
    {

        $attempt = 0;

        $client = new Client([
            'base_uri' => self::BASE_URL,
            'headers' => [
                'Authorization' => ['Basic ' . base64_encode('esbt:#exposedws-cl')],
//                'Authorization' => ['Basic ' . base64_encode('esbt:#exposedws-pr')],
            ]
        ]);

        $body = [
            "info" => [
                "idTransazione" => $this->infrastructure->getCustomerBucket()->getUid(),
                "idFlusso" => $invoiceId,
                "applicativo" => "TEAMSYSTEM",
                "flusso" => "ARBITRATO",
                "servizio" => "INTEGRAZIONI",
                "metodo" => "NOTIFICA_ORDINE",
                "evento" => "NOTIFICA_ORDINE",
                "codiceAzienda" => "4073",
                "mailReferente" => $this->params->mail_referente ?? null,
                "login" => $this->params->username ?? null,
                "password" => $this->params->password ?? null,
                "retryAutomatico" => false,
                "asincrono" => false,
                "urlRisposta" => ""
            ],
            "data" => [
                "map" => [
                    "soggetto" => $data['subject'],
                    "testata" => array_merge(
                        $data['invoice'],
                        ["righe" => $data['movements']]
                    )
                ]
            ],
            "files" => []
        ];


        do {
            try {
                $response = $client->post('', [
                    'json' => $body
                ]);
                break;
            } catch (Exception $e) {
                $attempt++;
                usleep(self::ATTEMPT_TIMEOUT);
            }
        } while ($attempt < self::MAX_ATTEMPT);

        if(!empty($response)) {
            $content = json_decode($response->getBody()->getContents(), false);
        }

        if (empty($response) || !filter_var($content->result->esito, FILTER_VALIDATE_BOOLEAN)) {

            $errorMessages = array_column($content->result->errorList, 'error');
            $allErrorMessages = implode("\n", $errorMessages);
            $this->applogger->err("Dynamics 365: \n" . $allErrorMessages);

            return [
                'error' => true,
                'message' => $allErrorMessages
            ];
        }

        return [
            'error' => false,
            'message' => ''
        ];

    }

}