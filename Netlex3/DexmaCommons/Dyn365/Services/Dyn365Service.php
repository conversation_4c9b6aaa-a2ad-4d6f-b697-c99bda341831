<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Dyn365\Services;

use <PERSON>ma<PERSON>om<PERSON>\Dyn365\Classes\DynTranslate;
use <PERSON>maCom<PERSON>\Dyn365\Classes\Invoice;
use DexmaCom<PERSON>\Dyn365\Classes\InvoiceAnagraficaAdapter;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dyn365\Classes\InvoiceDatiStudioAdapter;
use DexmaCom<PERSON>\Dyn365\Classes\InvoiceMovement;
use DexmaCom<PERSON>\Dyn365\Classes\InvoicePaSubjectAdapter;
use DexmaCommons\Dyn365\Enum\EnumTrascodeDyn365;
use DexmaCommons\Dyn365\Handlers\Dyn365Handler;
use Netlex3\Software\patterns\Domains\Invoice\Services\InvoiceService;
use Netlex3\Software\patterns\Domains\TabellePersonalizzate\Services\TabellePersonalizzateService;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;
use Netlex3\Software\patterns\Services\Archive\ArchiveService;

final class Dyn365Service
{

    private static $instance = NULL;
    private $infrastucture;

    public static function getInstance(InfrastructureInterface $infrastructure): Dyn365Service {
        if(self::$instance === NULL){
            self::$instance = new Dyn365Service($infrastructure);
        }
        return self::$instance;
    }

    private function __construct(InfrastructureInterface $infrastructure)
    {
        $this->infrastucture = $infrastructure;
    }

    public function sendInvoice(string $invoiceUniqueId){

        $movements = [];
        $subject = null;

        $invoiceService = InvoiceService::getInstance($this->infrastucture);
        $archiveService = ArchiveService::getInstance($this->infrastucture);
        $tabellePersonalizzateService = TabellePersonalizzateService::getInstance($this->infrastucture);
        $invoiceData = $invoiceService->findInvoiceByUniqueId($invoiceUniqueId);
        $archive = $archiveService->findArchiveByIdList([$invoiceData['codicepratica']])[0];

        $invoiceData['archive_code'] = $archive['codicearchivio'];

        if(empty($invoiceData)){
            return [
                'error' => true,
                'message' => 'Fattura non trovata'
            ];
        }


        // esistono altre alternative?
        if((int)$invoiceData['tipointestatario'] !== 3 && (int)$invoiceData['tipointestatario'] !== 5) {
            $subject = (new InvoiceAnagraficaAdapter($this->infrastucture))->findById((int)$invoiceData['codiceintestatario']);
        }

        if((int)$invoiceData['tipointestatario'] === 3){
            $subject = (new InvoicePaSubjectAdapter($this->infrastucture))->findById((int)$invoiceData['codiceintestatario']);
        }

        if((int)$invoiceData['tipointestatario'] === 5){
            $subject = (new InvoiceDatiStudioAdapter($this->infrastucture))->findById((int)$invoiceData['avvocatoemittente']);
        }

        $invoiceMovements = $invoiceService->findInvoiceMovementsById($invoiceData['id']);

        foreach ($invoiceMovements as $key=>$movement){

            if (!empty($movement['spesafissa_id'])) {
                    $foundExpenditure = $tabellePersonalizzateService
                    ->getFixedCostById($movement['spesafissa_id']);
                    $movement['external_code'] = $foundExpenditure['external_code'];
                    $movement['vat_code'] = $foundExpenditure['vat_code'];
            }
//
            $movement['archive_code'] = $archive['codicearchivio'];
            $movements[] = new InvoiceMovement($key+1, $movement);
        }

        $invoiceData["sezionale_iva"] = $invoiceData['sezionale'] !== -1 ?
            $tabellePersonalizzateService->getSezionaleFatturaById((int)$invoiceData['sezionale'])['external_code']:
            "";

        $invoiceData['payment_method'] = $subject->payment_method ?? 'MP01';

        $invoice = new Invoice(
            $this->infrastucture->getUser(),
            $invoiceData,
            $subject,
            $movements);

        $data = $invoice->convertTo(new DynTranslate($invoice, EnumTrascodeDyn365::TRANSCODE));
        $handler = new Dyn365Handler($this->infrastucture);

       return $handler->sendInvoice($invoiceData['id'], $data);
    }
}